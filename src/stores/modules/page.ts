import { defineStore } from 'pinia'
interface OnePageState {
  selectedArea: any
  selectedPlantName: string | null
  cityDicts: any[]
  countyDicts: any[]
  pageCurrent: number
}

export const usePageStore = defineStore('page', () => {
  const state: Ref<OnePageState> = ref({
    selectedArea: null,

    selectedPlantName: null,
    cityDicts: [],
    countyDicts: [],
    pageCurrent: 0
  })

  // getters
  const selectedArea = computed(() => state.value.selectedArea)

  const selectedPlantName = computed(() => state.value.selectedPlantName)
  const cityDicts = computed(() => {
    let data = state.value.cityDicts
    data = data.sort((a, b) => {
      return Number(a.ccode) - Number(b.ccode)
    })
    return data
  })
  const countyDicts = computed(() => state.value.countyDicts)
  const pageCurrent = computed(() => state.value.pageCurrent)

  // actions
  const setSelectedArea = (areas: any) => {
    state.value.selectedArea = areas
  }

  const setPlantName = (name: string) => {
    state.value.selectedPlantName = name
  }

  const setAreaDicts = (data, type) => {
    switch (type) {
      case 'city':
        state.value.cityDicts = data
        break
      case 'county':
        state.value.countyDicts = data
        break
    }
  }

  const setAreaByCode = (code: string) => {
    const city = state.value.cityDicts.find((item) => item.ccode === code)
    state.value.selectedArea = city
  }

  const setPageCurrent = (page: number) => {
    state.value.pageCurrent = page
  }

  const clearSelectedArea = () => {
    state.value.selectedArea = null
  }

  const clearPlantName = () => {
    state.value.selectedPlantName = null
  }

  const getCityInfo = (code: string) => {
    const info = state.value.cityDicts.find((item) => item.ccode === code)
    return info
  }

  return {
    // state
    selectedArea,
    cityDicts,
    countyDicts,
    selectedPlantName,
    pageCurrent,

    // actions
    setSelectedArea,
    setPlantName,
    setAreaDicts,
    setAreaByCode,
    setPageCurrent,

    // clear
    clearSelectedArea,
    clearPlantName,

    // get

    getCityInfo
  }
})
