<template>
  <div class="top-nav">
    <div class="flex items-center justify-center">
      <div
        class="left-button button-base z-[1] left-[90px]"
        :class="{ 'left-button-active': isActive(0) }"
        @click="changeTab(0)"
      >
        物种百科
      </div>
      <div class="title hansans-bold">湖北省农业野生植物科普宣传系统</div>
      <div
        class="right-button button-base z-[1]"
        :class="{ 'right-button-active': isActive(1) }"
        @click="changeTab(1)"
      >
        资源分布
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { usePageStore } from '@/stores'

defineOptions({ name: 'TopNav' })

const pageStore = usePageStore()

const isActive = (index: number) => pageStore.pageCurrent === index

function changeTab(index: number) {
  pageStore.setPageCurrent(index)
}
</script>

<style lang="less" scoped>
.top-nav {
  width: 100%;
  height: 100px;
  background-image: url('@/assets/images/onemap/header.png');
  background-size: 100% 100%;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1000;
}
.button-base {
  width: 310px;
  height: 60px;
  background-size: 100% 100%;
  font-size: 22px;
  font-weight: bold;
  padding-top: 12px;
  display: flex;
  margin-top: 20px;
  cursor: pointer;
  color: #91b9bc;
  position: relative;
}
.left-button {
  background-image: url('@/assets/images/onemap/menu-left-off.png');
  padding-left: 84px;
  &:hover {
    background-image: url('@/assets/images/onemap/menu-left-on.png');
    color: #7effe8;
  }
}
.right-button {
  background-image: url('@/assets/images/onemap/menu-right-off.png');
  padding-right: 84px;
  justify-content: flex-end;
  &:hover {
    background-image: url('@/assets/images/onemap/menu-right-on.png');
    color: #7effe8;
  }
}
.left-button-active {
  background-image: url('@/assets/images/onemap/menu-left-on.png') !important;
  color: #7effe8;
}
.right-button-active {
  background-image: url('@/assets/images/onemap/menu-right-on.png') !important;
  color: #7effe8;
}
.title {
  width: 900px;
  text-align: center;
  color: transparent;
  background-image: linear-gradient(to bottom, #fff 20%, #00c1f6);
  -webkit-background-clip: text;
  background-clip: text;
  display: inline-block;
  font-size: 38px;
  font-weight: bold;
  user-select: none;
}
</style>
