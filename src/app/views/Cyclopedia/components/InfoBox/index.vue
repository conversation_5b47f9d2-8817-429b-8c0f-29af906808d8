<template>
  <div class="info-box" :style="{ width: width, marginBottom: gap }">
    <div class="info-box-title">
      <div class="text">{{ title }}</div>
      <div class="sub-title" @click="onClickSubTitle">{{ subTitle || '' }}</div>
    </div>
    <div class="info-box-content" :style="{ height: height }">
      <div class="content">
        <slot></slot>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
defineOptions({ name: 'InfoBox' })

const emit = defineEmits(['update:open'])

interface Props {
  title: string
  subTitle?: string
  width?: string
  height?: string
  gap?: string
}

withDefaults(defineProps<Props>(), {
  width: '100%',
  height: '100%',
  gap: '20px'
})

const onClickSubTitle = () => {
  emit('update:open', 'subTitleClicked')
}
</script>

<style lang="less" scoped>
.info-box {
  overflow: hidden;

  .info-box-title {
    width: 100%;
    height: 44px;
    padding-left: 11%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    color: #333;
    background-image: url('@/assets/images/onemap/title-box.png');
    background-size: 100% 100%;
    margin-bottom: 6px;
    // 渐变文字
    .text {
      font-size: 22px;
      font-weight: 500;
      color: transparent;
      background-image: linear-gradient(to bottom, #fff 20%, #00c1f6);
      -webkit-background-clip: text;
      background-clip: text;
      display: inline-block;
      position: relative;
    }

    .sub-title {
      font-size: 14px;
      color: #8bb4b9;
      margin-right: 100px;
    }
  }

  .info-box-content {
    background-image: url('@/assets/images/onemap/data-bg.png');
    background-size: 100% 100%;
    flex: 1;
    padding: 10px 14px;
    .content {
      height: 100%;
      overflow: auto;
    }
  }
}
</style>
