<template>
  <div class="px-2 w-full h-[200px]">
    <div v-if="props.video && props.video.length" id="player"></div>
    <div
      v-else
      class="h-full flex flex-col justify-center items-center text-center text-map-simple-blue bg-[#181818] border border-map-simple-blue"
    >
      暂无视频！
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, watch, nextTick } from 'vue'
import Player from 'xgplayer'
import 'xgplayer/dist/index.min.css'

const props = defineProps<{
  video: Array<any> | null
}>()

let player: any = null

const initPlayer = () => {
  if (props.video && props.video.length) {
    const baseUrl = import.meta.env.VITE_APP_AXIOS_BASE_URL
    const url = `${baseUrl}/attachment/download/${props.video[0].uid}`
    const playerOpts = {
      id: 'player',
      url,
      fluid: true,
      cssFullscreen: false,
      fullscreen: true,
      playbackRate: false,
      volume: 0
    }
    player = new Player(playerOpts)
  }
}

onMounted(() => {
  nextTick(initPlayer)
})

watch(
  () => props.video,
  () => {
    nextTick(initPlayer)
  }
)
</script>
