import { LayerPopup, PointLayer, Scene } from '@antv/l7'
import { formDataApi } from '@gt/mis-components-web/apis'
import {
  createBaseLayer,
  createBoundaryLayer,
  createLineLayer,
  createMaskLayer,
  createPointLayer,
  createTextLayer,
  extrudeHeight
} from './useMapLayers'

// 引入图片
import mapMarkerIcon from '@/assets/images/onemap/map-marker.png'
import mapMarker1Icon from '@/assets/images/onemap/map-marker1.png'
import mapMarker2Icon from '@/assets/images/onemap/map-marker2.png'
import mapMarker3Icon from '@/assets/images/onemap/map-marker3.png'

interface InitFormDataOptions {
  /** 是否显示错误信息 */
  showError?: boolean
  /** 加载进度回调函数 */
  onProgress?: (completed: number, total: number) => void
}

/**
 * 初始化加载所有数据
 * @param {InitFormDataOptions} options - 可选配置参数
 * @returns {Promise<Record<string, any[]>>} 返回所有表单数据的映射对象
 */
export async function initFormData(options: InitFormDataOptions = { showError: true }) {
  const formNameList = [
    'protection_point_list',
    'wild_plant_list',
    'plant_distribution_list',
    'protection_point_monitoring',
    'protection_point_development',
    'protection_point_scientific',
    'protection_point_species_status',
    'wild_plant_info',
    'admin_city',
    'admin_county'
  ]

  // 创建请求列表
  const requestList = formNameList.map((formName) => {
    return formDataApi
      .searchFormData(formName)
      .then((result) => ({
        status: 'fulfilled' as const,
        value: { ...result, formName }
      }))
      .catch((error) => {
        if (options.showError) {
          console.error(`加载表单数据 ${formName} 失败:`, error)
        }
        return {
          status: 'rejected' as const,
          reason: error,
          value: { list: [], total: 0, formName }
        }
      })
  })

  let completed = 0
  const total = requestList.length + 4 // 加上4个geojson文件

  // 使用 Promise.all 处理所有表单数据请求
  const formDataResults = await Promise.all(requestList)
  const data: Record<string, any[]> = {}

  formDataResults.forEach((result) => {
    completed++
    if (options.onProgress) {
      options.onProgress(completed, total)
    }

    data[result.value.formName] = result.value.list
  })

  // 表单数据请求完成后，再请求geojson数据
  const geojsonPromises = [
    fetch(`${import.meta.env.VITE_APP_URL}gis/protect_area.geojson`).then((res) => res.json()),
    fetch(`${import.meta.env.VITE_APP_URL}gis/province.geojson`).then((res) => res.json()),
    fetch(`${import.meta.env.VITE_APP_URL}gis/city.geojson`).then((res) => res.json()),
    fetch(`${import.meta.env.VITE_APP_URL}gis/county.geojson`).then((res) => res.json())
  ]

  const [protect_area, province, city, county] = await Promise.all(geojsonPromises)

  // 更新进度
  completed += 4
  if (options.onProgress) {
    options.onProgress(completed, total)
  }

  // 将geojson数据添加到返回结果中
  data.protect_area_geojson = protect_area
  data.province_geojson = province
  data.city_geojson = city
  data.county_geojson = county

  return data
}

export async function initSceneLayers(scene: Scene, data: any) {
  // 添加图标图片
  await addSceneImage(scene)
  // 获取geojson数据
  const city = data.city_geojson
  const county = data.county_geojson

  // 构造基础图层
  // 构造图层
  const maskLayer = createMaskLayer(county, { name: 'base-mask' })
  const boundaryLayer = createBoundaryLayer(city, { name: 'base-boundary' })
  const baseLayer = createBaseLayer(county, { name: 'base-layer' })
  const cityLineLayer = createLineLayer(city, { name: 'base-city-line' })
  const countyLineLayer = createLineLayer(county, { name: 'base-county-line' })
  const countyLabelLayer = createTextLayer(data.admin_county, {
    name: 'base-county-label',
    zIndex: 999,
    key: 'fname'
  })
  const cityLabelLayer = createTextLayer(data.admin_city, {
    name: 'base-city-label',
    zIndex: 999,
    key: 'cname'
  })
  countyLineLayer.size(1).color('rgba(255,255,255,0.2)')

  countyLabelLayer.hide()

  scene.addLayer(maskLayer)
  scene.addLayer(boundaryLayer)
  scene.addLayer(baseLayer)
  scene.addLayer(cityLineLayer)
  scene.addLayer(cityLabelLayer)
  scene.addLayer(countyLineLayer)
  scene.addLayer(countyLabelLayer)

  // 资源分布保护点
  const scienceBhdPoint = createPointLayer([], {
    name: 'science-bhd-point',
    zIndex: 100,
    shape: 'point',
    size: 20
  })
  scene.addLayer(scienceBhdPoint)
  // 添加popup
  const ScienceBhdPointPopup = new LayerPopup({
    items: [
      {
        layer: scienceBhdPoint as any,
        customContent: (data: any) => {
          console.log('data', data)
          const wzmcStr = Array.isArray(data.wzmc) ? data.wzmc.join('、') : data.wzmc || ''

          return `<div>保护点名称：${data.bhdmc}</div>
                  <div>所在区县：${data.cname}${data.xname}</div>
                  <div>主要保护物种：${wzmcStr}</div>
                  <div>建设年份：${data.year || '-'}</div>
                  <div>保护点状态：${data.bhdzt || '-'}</div>
          `
        }
      }
    ]
  })
  scene.addPopup(ScienceBhdPointPopup)
}

function addSceneImage(scene: Scene) {
  scene.addImage('point', mapMarkerIcon)
  scene.addImage('marker1', mapMarker1Icon)
  scene.addImage('marker2', mapMarker2Icon)
  scene.addImage('marker3', mapMarker3Icon)
}
