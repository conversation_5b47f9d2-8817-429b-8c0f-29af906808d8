<template>
  <div>
    <div class="w-[330px] fixed top-[150px] left-[70px] flex flex-col gap-2.5">
      <PlantSelect v-show="!pageStore.selectedPlantName" :value="plantName" @change="changePlant" />
      <AreaSelect />
    </div>

    <div
      v-if="pageStore.selectedPlantName"
      class="fixed top-[110px] h-[50px] left-1/2 transform -translate-x-1/2"
    >
      <div class="flex items-center rounded-lg">
        <div class="flex items-center cursor-pointer" @click="handleBack">
          <SvgIcon class="ml-4 text-map-light-blue text-title" icon="ArrowLeftOutlined" />
          <div class="text-map-light-blue text-title ml-2">返回</div>
        </div>
        <div class="text-white mx-4">丨</div>
        <div class="text-title text-white" style="text-shadow: 0 0 2 #000">
          {{ title }}
        </div>
      </div>
    </div>

    <div class="legend-container">
      <div class="legend-item">
        <img class="w-[30px]" src="@/assets/images/onemap/map-marker.png" />
        <div class="mx-2 text-map-simple-blue text-[16px]">原生境保护点</div>
      </div>

      <div class="legend-item" v-if="plantName">
        <div class="legend-icon">
          <div class="w-[10px] h-[10px] bg-map-light-blue"></div>
        </div>
        <div class="mx-2 text-map-simple-blue text-[16px]">{{ plantName }}植物分布</div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
defineOptions({ name: 'Distribution' })
import { usePageStore } from '@/stores'
import { Scene } from '@antv/l7'

import PlantSelect from '../../components/PlantSelect/index.vue'
import AreaSelect from '../../components/AreaSelect/index.vue'

import { sceneKey, formDataKey, mapKey } from '@/app/views/Cyclopedia/config'

import {
  baseCenter,
  baseLayerSwitch,
  basePitch,
  baseZoom,
  pageLayerSwitch,
  initSceneLayerState
} from '@/app/views/Cyclopedia/hooks/useMapLayers'

const pageStore = usePageStore()

const scene = inject(sceneKey) as Ref<Scene>
const formData = inject(formDataKey) as any
const mapbox: any = inject(mapKey)

const layer = scene.value.getLayerByName('science-bhd-point')

const bhd_list = ref<any[]>([])
const distributionList = ref<any[]>([])
async function getFormData() {
  const { protection_point_list = [], plant_distribution_list = [] } = formData?.value || {}
  bhd_list.value = protection_point_list
  distributionList.value = plant_distribution_list
}

const title = computed(() => {
  let area = '湖北省'
  if (pageStore.selectedArea) {
    area = pageStore.selectedArea.fname || pageStore.selectedArea.cname
  }
  return `${area}${pageStore.selectedPlantName}分布图`
})

onMounted(() => {
  getFormData()
  addPointEvent()
})

const addPointEvent = () => {
  if (bhd_list.value.length > 0) {
    layer?.setData(bhd_list.value)
  }
}

const plantName = ref<string | null>(null)
function changePlant(value: string | null) {
  plantName.value = value

  const selectedArea = pageStore.selectedArea
  const ccode = selectedArea?.ccode

  layer?.filter('wzmc*ccode', (wzmc, code) => {
    const wzmcValid = !value || (Array.isArray(wzmc) && wzmc.includes(value))
    const cityValid = !selectedArea || code === ccode
    return wzmcValid && cityValid
  })

  updateDistributionColors(value, ccode)
  scene.value.render()
}

// 更新分布区域颜色
function updateDistributionColors(value: string | null, ccode: string) {
  const baseLayer = scene.value.getLayerByName('base-layer')
  let plants = distributionList.value.filter((item) => item.wzmc === value)

  if (pageStore.selectedArea) {
    plants = plants.filter((item) => item.ccode === ccode)
  }

  baseLayer.color('code', (code: string) => {
    const info = plants.find((item) => item.fcode === code)
    return info ? 'rgba(0, 255, 255, 0.4)' : 'rgba(0,0,0,0)'
  })
}

function initPageState() {
  pageLayerSwitch(scene.value, 'science')

  let plantName = pageStore.selectedPlantName
  if (plantName) {
    changePlant(plantName)
  } else {
    changePlant(null)
  }
}
watch(
  () => pageStore.pageCurrent,
  (val) => {
    if (val === 1) {
      initPageState()
    }
  }
)

const changeByArea = (val: string) => {
  if (val) {
    // 缩放到选中区域
    zoomToArea(val)
    // 调整图层显示
    adjustLayersForArea(val)
  } else {
    // 重置到默认视图
    resetToDefaultView()
  }

  changePlant(plantName.value)
  scene.value.render()
}
watch(
  () => pageStore.selectedArea,
  (val) => {
    changeByArea(val)
  }
)

function zoomToArea(area: any) {
  mapbox.value.fitBounds(
    [
      [area.x_min - 0.3, area.y_min - 0.3],
      [area.x_max + 0.3, area.y_max + 0.3]
    ],
    { pitch: 20 }
  )
}
function adjustLayersForArea(area: any) {
  const countyLineLayer = scene.value.getLayerByName('base-county-line')
  const cityLineLayer = scene.value.getLayerByName('base-city-line')
  const cityLabelLayer = scene.value.getLayerByName('base-city-label')
  const countyLabelLayer = scene.value.getLayerByName('base-county-label')
  const boundaryLayer = scene.value.getLayerByName('base-boundary')

  // 隐藏方块图层和市级图层

  cityLineLayer.hide()
  cityLabelLayer.hide()

  // 显示和配置县级图层
  countyLineLayer.size(2).color('#fff')
  countyLabelLayer.show()

  // 过滤显示当前区域
  countyLineLayer.color('ccode', (code: string) => {
    return code === area.ccode ? '#E5FFFF' : 'rgba(0,0,0,0)'
  })

  countyLabelLayer.filter('ccode', (code: string) => code === area.ccode)
  boundaryLayer.filter('ccode', (code: string) => code === area.ccode)
}

function resetToDefaultView() {
  mapbox.value.setPitch(basePitch)
  mapbox.value.flyTo({
    center: baseCenter,
    zoom: baseZoom
  })
  // scene场景初始状态
  initSceneLayerState(scene.value)
  // 显示资源分布模块点位
  pageLayerSwitch(scene.value, 'science')
}

// 返回物种百科
const handleBack = () => {
  pageStore.clearSelectedArea()
  pageStore.clearPlantName(null)
  pageStore.setPageCurrent(0)
}
</script>

<style lang="less" scoped>
.year-box {
  width: 84px;
  height: 31px;
  background: #34b6fa;
  border-radius: 2px;
  border: 1px solid #34b6fa;
  border-left: none;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  color: #fff;
  font-size: 18px;
  transform: translateY(25%);
}

.year-box::before {
  content: '';
  position: absolute;
  left: -6px;
  top: 50%;
  transform: translateY(-50%);
  width: 0;
  height: 0;
  border-top: 4px solid transparent;
  border-bottom: 4px solid transparent;
  border-right: 6px solid #34b6fa;
}

:deep(.ant-timeline-item-head) {
  background-color: transparent !important;
}

:deep(.ant-timeline-item) {
  padding-bottom: 25px !important;
}

:deep(.ant-timeline-item-tail) {
  border-inline-start: 2px dashed #a6c1d3 !important;
}

:deep(.ant-timeline-item-head-custom) {
  transform: translate(-50%, 0%) !important;
}

.legend-container {
  position: absolute;
  bottom: 40px;
  left: 40px;
}

.legend-item {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.legend-icon {
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.legend-color {
  width: 14px;
  height: 8px;
  background-color: var(--primary-color);
}

.chart-legend {
  margin-top: 8px;
  padding-left: 10px;
}
</style>
