<template>
  <div>
    <div class="fixed top-0 left-0 right-0 bottom-0 bg-[#175F72] opacity-[1]"></div>
    <div v-show="!modal">
      <div class="fixed top-[110px] left-[70px]">
        <PlantSelectHistory :value="plantName" @change="changePlant" />
      </div>
      <div class="fixed top-[155px] left-[70px] right-[70px] bottom-0 overflow-auto pb-4">
        <div class="grid grid-cols-4 gap-4">
          <div
            v-for="item in dataList"
            :key="item.id || item.wzmc"
            class="bg-[#05171e66] rounded-lg shadow p-4 flex flex-col text-map-simple-blue text-base"
          >
            <img
              :src="getFirstPhotoUrl(item, defaultImg)"
              class="w-full h-[280px] object-cover rounded mb-2 border"
            />
            <div class="flex items-center gap-x-[10px] mb-2">
              <div class="font-bold text-xl">{{ item.wzmc }}</div>
              <div>
                <a-tag color="#175F72">{{ '国家' + item.xbhdj }}</a-tag>
              </div>
            </div>
            <div class="flex justify-between text-base">
              <div class="flex gap-x-4">
                <div>
                  <span>科：</span>
                  <span class="text-black">{{ item.species || '未知' }}</span>
                </div>
                <div>
                  <span>属：</span>
                  <span class="text-black">{{ item.genus || '未知' }}</span>
                </div>
              </div>

              <a-button type="link" size="small" @click="handleClick(item)">查看更多</a-button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div v-show="modal" class="fixed top-[110px] left-[70px] right-[70px] h-[960px]">
      <div class="flex h-full gap-x-3 text-base">
        <div class="relative w-[380px] text-map-simple-blue bg-[#05171e66]">
          <div class="pt-4 px-2">
            <div class="title text-xl font-bold px-2">基本信息</div>
            <div v-for="item in fields" :key="item.field" class="flex items-start py-2 px-2">
              <span class="w-[100px] font-medium flex-shrink-0">{{ item.label }}：</span>
              <div class="max-h-[4.5em] overflow-y-auto leading-[1.5]">
                <span class="break-all">{{ plantInfo?.[item.field] || '-' }}</span>
              </div>
            </div>
          </div>

          <div class="absolute bottom-[350px] left-0 right-0">
            <div class="title text-xl font-bold px-4 pb-2">物种视频</div>
            <VideoPlayer :video="plantInfo?.video" />
          </div>

          <div class="absolute bottom-[20px] left-[0px] right-[0px] w-[380px] h-[300px]">
            <div class="title text-xl font-bold px-4">物种分布</div>
            <div
              class="text-base text-right text-map-simple-blue px-4 cursor-pointer mt-2"
              @click="expandDistribution"
            >
              展开详细
            </div>

            <SmallMap ref="SmallMapRef" @mapLoad="smallMapLoad" />
          </div>
        </div>

        <div class="relative flex-1 bg-[#05171e66]">
          <div class="flex justify-between items-center px-6 py-4 border-b border-[#223]">
            <div class="flex gap-x-8 text-lg font-semibold text-white">
              <div
                v-for="tab in tabList"
                :key="tab.value"
                class="cursor-pointer px-2 py-1 rounded"
                :class="
                  activeTab === tab.value ? 'bg-[#175F72] text-white' : 'hover:text-[#175F72]'
                "
                @click="selectTab(tab.value)"
              >
                {{ tab.label }}
              </div>
            </div>
            <div>
              <img :src="returnImg" class="w-[35px] h-[35px] cursor-pointer" @click="closeModel" />
            </div>
          </div>
          <div>
            <PlantPhotoCarousel
              v-show="activeTab === 'feature'"
              :photoFields="photoFields"
              :photos="plantInfo"
            />
            <div
              v-show="activeTab === 'environment'"
              class="p-[20px] text-map-simple-blue text-base"
            >
              <div class="mb-[20px]">
                <div class="break-all">{{ plantInfo?.szhj || '-' }}</div>
              </div>
            </div>
            <div v-show="activeTab === 'value'" class="p-[20px] text-map-simple-blue text-base">
              <div v-for="item in speciesValue" :key="item.field" class="mb-[20px]">
                <div class="font-medium">{{ item.label }}：</div>
                <div class="break-all">{{ plantInfo?.[item.field] || '-' }}</div>
              </div>
            </div>
            <div v-show="activeTab === 'usage'" class="p-[20px] text-map-simple-blue text-base">
              <div v-for="item in mainUse" :key="item.field">
                <div class="font-medium">{{ item.label }}：</div>
                <div class="break-all">{{ plantInfo?.[item.field] || '-' }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
defineOptions({ name: 'Encyclopedia' })
import PlantSelectHistory from './components/PlantSelectHistory/index.vue'
import PlantPhotoCarousel from './components/PlantPhotoCarousel/index.vue'
import VideoPlayer from '../../components/VideoPlayer/index.vue'
import SmallMap from './components/Map/index.vue'
import defaultImg from '@/assets/images/onemap/noImg.png'
import returnImg from '@/assets/images/onemap/return.png'
import { formDataKey } from '../../config'
import { usePageStore } from '@/stores'

const pageStore = usePageStore()
const formData = inject(formDataKey)

onMounted(() => {
  getFormData()
})

const dataList = ref<any[]>([])
const distributionList = ref<any[]>([])
const getFormData = () => {
  try {
    const { wild_plant_info = [], plant_distribution_list = [] } = formData?.value || {}

    distributionList.value = plant_distribution_list

    dataList.value = (wild_plant_info || []).map((item) => ({
      ...item,
      base_photo: item.base_photo ? mapImageUrls(item.base_photo) : [],
      root_photo: item.root_photo ? mapImageUrls(item.root_photo) : [],
      plant_photo: item.plant_photo ? mapImageUrls(item.plant_photo) : [],
      branch_photo: item.branch_photo ? mapImageUrls(item.branch_photo) : [],
      stem_photo: item.stem_photo ? mapImageUrls(item.stem_photo) : [],
      leaf_photo: item.leaf_photo ? mapImageUrls(item.leaf_photo) : [],
      fruit_photo: item.fruit_photo ? mapImageUrls(item.fruit_photo) : []
    }))
  } catch (error) {
    console.error('获取数据失败:', error)
  }
}

function getFirstPhotoUrl(item, defaultImg) {
  const fields = [
    'base_photo',
    'plant_photo',
    'leaf_photo',
    'flower_photo',
    'stem_photo',
    'fruit_photo',
    'root_photo',
    'branch_photo'
  ]
  for (const field of fields) {
    const url = item[field]?.[0]?.url
    if (url) return url
  }
  return defaultImg
}

const mapImageUrls = (arr: any[]) => {
  if (!Array.isArray(arr)) return []
  const baseUrl = import.meta.env.VITE_APP_AXIOS_BASE_URL
  return arr.map((item) => ({
    ...item,
    url: `${baseUrl}/attachment/download/${item.uid}`
  }))
}

const plantName = ref('')
const changePlant = (value: string | null) => {
  if (!value) {
    getFormData()
    plantName.value = ''
    return
  } else {
    dataList.value = dataList.value.filter((item) => item.wzmc.includes(value))
    plantName.value = value
  }
}

const SmallMapRef = ref()
const smallMapLoaded = ref(false)
function smallMapLoad() {
  console.log('小地图加载完成')
  smallMapLoaded.value = true
}

const modal = ref(false)
const plantInfo = ref<any>(null)

const distributionMap = computed(() => {
  const map = new Map()
  for (const d of distributionList.value ?? []) {
    if (!map.has(d.wzmc)) map.set(d.wzmc, [])
    if (d.ccode) map.get(d.wzmc).push(d.ccode)
  }

  for (const [key, arr] of map) {
    map.set(key, Array.from(new Set(arr)))
  }
  return map
})

const handleClick = (item) => {
  plantInfo.value = item
  activeTab.value = 'feature'

  nextTick(() => {
    let cityCodes = distributionMap.value.get(item.wzmc) || []
    if (!cityCodes.length) {
      SmallMapRef.value?.setFillColor([])
      return
    }
    SmallMapRef.value?.setFillColor(cityCodes)
  })

  modal.value = true
}

const fields = [
  {
    label: '中文学名',
    field: 'wzmc'
  },
  {
    label: '拉丁学名',
    field: 'ldmc'
  },
  {
    label: '科',
    field: 'species'
  },
  {
    label: '属',
    field: 'genus'
  },
  {
    label: '生活型',
    field: 'shx'
  },
  {
    label: '识别要点',
    field: 'sbyd'
  }
]

const photoFields = [
  { field: 'plant', label: '株', img: 'plant_photo' },
  { field: 'leaf', label: '叶', img: 'leaf_photo' },
  { field: 'flower', label: '花', img: 'flower_photo' },
  { field: 'stem', label: '茎', img: 'stem_photo' },
  { field: 'fruit', label: '果', img: 'fruit_photo' },
  { field: 'root', label: '根', img: 'root_photo' },
  { field: 'branch', label: '枝', img: 'branch_photo' }
]

const tabList = [
  { label: '形态特征', value: 'feature' },
  { label: '生长环境', value: 'environment' },
  { label: '物种价值', value: 'value' },
  { label: '主要用途', value: 'usage' }
]
const activeTab = ref(tabList[0].value)
const selectTab = (val: string) => {
  activeTab.value = val
}

// 物种价值
const speciesValue = ref([
  { label: '营养价值', field: 'nutrition' },
  { label: '药用价值', field: 'yyjz' },
  { label: '经济价值', field: 'jjjz' },
  { label: '观赏价值', field: 'gsjz' },
  { label: '科研价值', field: 'kyjz' },
  { label: '生态作用', field: 'stzy' }
])

// 主要用途
const mainUse = ref([{ label: '主要用途', field: 'zyyt' }])

// 返回
const closeModel = () => {
  modal.value = false
  plantInfo.value = {}
}

// 跳转
const expandDistribution = () => {
  pageStore.setPlantName(plantInfo.value.wzmc)
  pageStore.setPageCurrent(1)
}
</script>

<style land="less" scoped>
:deep(.ant-btn-link) {
  color: #15fffc !important;
}

.title {
  background: linear-gradient(to bottom, #fff 20%, #00c1f6);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  color: transparent;
}
</style>
