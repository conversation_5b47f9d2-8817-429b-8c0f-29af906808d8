<template>
  <div>
    <div class="flex items-center">
      <span class="font-bold text-map-simple-blue text-base tracking-wide mr-4">
        农业重点保护野生植物:
      </span>
      <a-input-group compact class="w-[550px]">
        <a-select
          v-model:value="showType"
          class="w-[180px] mr-4"
          :options="plantType"
          placeholder="植物类型"
          @change="getFormData"
        ></a-select>

        <a-select
          class="w-[330px] mr-4"
          v-model:value="showValue"
          show-search
          allowClear
          placeholder="请选择植物"
          :default-active-first-option="false"
          :show-arrow="false"
          :filter-option="false"
          :not-found-content="null"
          :options="data"
          @search="handleSearch"
          @change="handleChange"
        ></a-select>
      </a-input-group>

      <div v-if="searchHistory.length" class="flex justify-center items-center text-[#A6C1D3]">
        <div>搜索历史：</div>
        <a-tag
          v-for="item in searchHistory"
          :key="item"
          @click="selectHistory(item)"
          closable
          @close="removeHistory(item)"
          style="cursor: pointer; margin-right: 8px"
        >
          {{ item }}
        </a-tag>
        <!-- <a class="clear-history" @click="clearHistory">清空</a> -->
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { formDataKey } from '@/app/views/Cyclopedia/config'

interface Props {
  value?: string | null
}

const props = defineProps<Props>()

const emit = defineEmits(['change'])
const formData = inject(formDataKey) as Ref<any>

// 植物类型
const plantType = [
  { value: '', label: '所有' },
  { value: '被子植物', label: '被子植物' },
  // { value: '裸子植物', label: '裸子植物' },
  { value: '石松类和蕨类植物', label: '石松类和蕨类植物' },
  { value: '苔藓植物', label: '苔藓植物' }
]

const data = ref([
  {
    label: '国家一级保护植物',
    options: []
  },
  {
    label: '国家二级保护植物',
    options: []
  }
])

const showType = ref('') // 植物类型选择
const showValue = ref() // 选择的植物

const columns = ref<any[]>([])

const getFormData = async (type) => {
  try {
    const list = formData.value.wild_plant_list.filter(
      (item) => item.sfnybmgl === '是' && (!type || item.fldw === type)
    )
    columns.value = list
    data.value = groupPlantsByLevel(list)
  } catch (error) {
    console.error('获取数据失败:', error)
  }
}

const groupPlantsByLevel = (list) => {
  const firstLevel: any = []
  const secondLevel: any = []
  list.forEach((item) => {
    if (item.xbhdj === '一级') {
      firstLevel.push({ label: item.wzmc, value: item.wzmc })
    } else {
      secondLevel.push({ label: item.wzmc, value: item.wzmc })
    }
  })

  // 过滤掉无数据的分组
  return [
    { label: '国家一级保护植物', options: firstLevel },
    { label: '国家二级保护植物', options: secondLevel }
  ].filter((group) => group.options.length > 0) // 关键修改：只保留有数据的分组
}
onMounted(() => {
  getFormData('')
  loadHistory()
})

const handleSearch = (val: string) => {
  if (!val) {
    data.value = groupPlantsByLevel(columns.value)
  } else {
    const filteredData = columns.value.filter((item) =>
      item.wzmc.toLowerCase().includes(val.toLowerCase())
    )
    data.value = groupPlantsByLevel(filteredData)
  }
}
const handleChange = (val: string) => {
  emit('change', val)
  data.value = groupPlantsByLevel(columns.value)
  saveHistory(val)
}

watch(
  () => props.value,
  (val) => {
    showValue.value = val
  }
)

const HISTORY_KEY = 'plant_search_history'
const searchHistory = ref<string[]>([])

const loadHistory = () => {
  const h = localStorage.getItem(HISTORY_KEY)
  searchHistory.value = h ? JSON.parse(h) : []
}
const saveHistory = (val: string) => {
  if (!val) return
  let arr = searchHistory.value.filter((item) => item !== val)
  arr.unshift(val)
  if (arr.length > 8) arr = arr.slice(0, 8)
  searchHistory.value = arr
  localStorage.setItem(HISTORY_KEY, JSON.stringify(arr))
}
const clearHistory = () => {
  searchHistory.value = []
  localStorage.removeItem(HISTORY_KEY)
}
const selectHistory = (val: string) => {
  showValue.value = val
  emit('change', val)
}

const removeHistory = (val: string) => {
  const arr = searchHistory.value.filter((item) => item !== val)
  searchHistory.value = arr
  localStorage.setItem(HISTORY_KEY, JSON.stringify(arr))
}
</script>

<style lang="less" scoped>
:deep(.ant-tag) {
  color: #a6c1d3;

  .ant-tag-close-icon {
    color: #a6c1d3;
  }
}
</style>
