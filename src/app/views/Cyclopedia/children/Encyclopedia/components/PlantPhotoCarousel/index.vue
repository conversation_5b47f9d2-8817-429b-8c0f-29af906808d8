<template>
  <div class="w-full h-full p-[20px] relative">
    <div class="flex flex-col">
      <div class="h-[85px] mb-2 text-map-simple-blue text-base flex items-start">
        <span class="flex-shrink-0 mr-2">{{ currentField.label }}：</span>
        <div class="flex-1 max-h-[4.5em] overflow-y-auto leading-[1.5] break-all">
          {{ photos?.[currentField.field] || '-' }}
        </div>
      </div>
      <div class="flex gap-2 w-full h-[710px]">
        <template v-if="currentPhotos.length > 0">
          <a-image
            width="100%"
            height="100%"
            :preview="{
              visible: previewVisible,
              onVisibleChange: handlePreviewVisibleChange,
              getContainer: 'body'
            }"
            :src="currentPhotos.length > 1 ? currentPhotos[photoIndex].url : currentPhotos[0].url"
          />
        </template>
        <img class="w-full h-full object-cover" v-else src="@/assets/images/onemap/noImg.png" />
      </div>
    </div>

    <div
      class="absolute left-1/2 -bottom-[40px] transform -translate-x-1/2 flex justify-center gap-2 z-50"
    >
      <div
        v-for="(item, idx) in photoFields"
        :key="item.field"
        class="px-3 py-1 rounded cursor-pointer"
        :class="isTabActive(idx) ? 'bg-[#175F72] text-map-simple-blue' : 'bg-[#fff]'"
        @mouseenter="selectTab(idx)"
        @mouseleave="resumeAutoPlay"
      >
        {{ item.label }}
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
const props = defineProps<{
  photoFields: Array<{ field: string; label: string; img: string }>
  photos: Record<string, any>
}>()

watch(
  () => props.photos,
  () => {
    autoIndex.value = 0
    photoIndex.value = 0
  },
  { deep: true }
)

// 轮播相关状态
const activeIndex = ref<null | number>(null) // 悬浮选中的字段索引
const autoIndex = ref(0) // 自动轮播字段索引
const photoIndex = ref(0) // 当前字段下图片索引

let fieldTimer: ReturnType<typeof setTimeout> | null = null
let photoTimer: ReturnType<typeof setInterval> | null = null

// 单张图片展示时长（ms）
const photoInterval = 5000

const currentField = computed(() =>
  activeIndex.value !== null
    ? props.photoFields[activeIndex.value]
    : props.photoFields[autoIndex.value]
)

const currentPhotos = computed(() => getPhotos(currentField.value))

function getPhotos(field: { img: string }) {
  const arr = props.photos?.[field.img]
  return Array.isArray(arr) ? arr : []
}

function isTabActive(idx: number) {
  return activeIndex.value === idx || (activeIndex.value === null && autoIndex.value === idx)
}

// 字段 轮播
function startFieldAutoPlay() {
  stopFieldAutoPlay()
  const interval = Math.max(currentPhotos.value.length, 1) * photoInterval
  fieldTimer = setTimeout(() => {
    autoIndex.value = (autoIndex.value + 1) % props.photoFields.length
    startFieldAutoPlay()
  }, interval)
}
function stopFieldAutoPlay() {
  if (fieldTimer) {
    clearTimeout(fieldTimer)
    fieldTimer = null
  }
}

// 图片自动轮播
function startPhotoAutoPlay() {
  stopPhotoAutoPlay()
  if (currentPhotos.value.length > 1) {
    photoTimer = setInterval(() => {
      photoIndex.value = (photoIndex.value + 1) % currentPhotos.value.length
    }, photoInterval)
  }
}
function stopPhotoAutoPlay() {
  if (photoTimer) {
    clearInterval(photoTimer)
    photoTimer = null
  }
}

// 悬浮
function selectTab(idx: number) {
  activeIndex.value = idx
  stopFieldAutoPlay()
}

function resumeAutoPlay() {
  if (activeIndex.value !== null) {
    autoIndex.value = activeIndex.value
  }
  activeIndex.value = null
}

watch(currentField, () => {
  photoIndex.value = 0
  startPhotoAutoPlay()
})

watch(activeIndex, (val) => {
  if (val === null) {
    startFieldAutoPlay()
  } else {
    stopFieldAutoPlay()
  }
})

onMounted(() => {
  startFieldAutoPlay()
})
onBeforeUnmount(() => {
  stopFieldAutoPlay()
  stopPhotoAutoPlay()
})

function pauseAllAutoPlay() {
  stopPhotoAutoPlay()
  stopFieldAutoPlay()
}
function resumeAllAutoPlay() {
  startPhotoAutoPlay()
  if (activeIndex.value === null) {
    startFieldAutoPlay()
  }
}

const previewVisible = ref(false)
function handlePreviewVisibleChange(visible) {
  console.log('visible', visible)

  previewVisible.value = visible
  if (visible) {
    pauseAllAutoPlay()
  } else {
    resumeAllAutoPlay()
  }
}
</script>
