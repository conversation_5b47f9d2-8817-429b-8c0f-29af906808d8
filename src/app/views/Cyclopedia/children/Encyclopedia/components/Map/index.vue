<template>
  <div id="encyclopedia-map" class="map" ref="mapContainer"></div>
</template>

<script lang="ts" setup>
import mapboxgl from '@/common/mapbox-gl/mapbox-gl'
import { formDataKey } from '@/app/views/Cyclopedia/config'

const emit = defineEmits(['mapLoad'])
const mapContainer = ref<HTMLDivElement>()
const formData = inject(formDataKey) as Ref<any>
const map = ref()
const tokenAry = [
  '7312e19f34ffbe026d8bcc9170b09b0e',
  'bb74c3ba588dab987a7dee5d30938ead',
  '2760a4b142a494210d757cc2d8358500',
  'c0032fdcdf378f6ab550aae9e5fb8495',
  '42c374a7d8836a1a7a5a4390d64d4226',
  'd2afb498a1b6923ac8759439202c1041',
  'fd807db71572159675e9617b45cb4108',
  '6a87ad7714e765da8107332559393fd1',
  'b48cc5d82c84b39d2bb171d537ea85c8'
]
function initMap() {
  const geojsonData: any = {
    type: 'FeatureCollection',
    features: formData.value.admin_city.map((item) => ({
      type: 'Feature',
      geometry: {
        type: 'Point',
        coordinates: [item.x, item.y]
      },
      properties: item
    }))
  }

  map.value = new mapboxgl.Map({
    container: mapContainer.value as HTMLDivElement,
    // style: 'http://39.104.87.15/api/basemap/style/default.json',
    // style: 'mapbox://styles/mapbox/dark-v10',
    style: {
      version: 8,
      glyphs: `https://api.mapbox.com/fonts/v1/mapbox/{fontstack}/{range}.pbf?access_token=${tokenAry[Math.floor(Math.random() * tokenAry.length)]}`,
      sources: {
        city: {
          type: 'geojson',
          data: formData.value.city_geojson
        },
        cityArea: {
          type: 'geojson',
          data: geojsonData
        }
      },
      layers: [
        {
          id: 'city-layer',
          type: 'fill',
          source: 'city',
          paint: {
            'fill-color': 'transparent',
            'fill-opacity': 0.5
          }
        },
        {
          id: 'city-line-layer',
          type: 'line',
          source: 'city',
          paint: {
            'line-color': '#fff'
          }
        },
        {
          id: 'city-label',
          type: 'symbol',
          source: 'cityArea',
          layout: {
            'text-field': ['get', 'cname'],
            'text-size': 10,
            'symbol-placement': 'point',
            'text-anchor': 'center'
          },
          paint: {
            'text-color': '#fff',
            'text-halo-width': 2
          }
        }
      ]
    },
    // center: baseCenter, // 湖北省中心点
    // zoom: 5,
    minZoom: 2,
    maxZoom: 15,
    bounds: [
      [107.97563527335012, 27.21640282109925],
      [116.32857134070377, 33.3722128254732]
    ],
    dragRotate: false // 禁用拖动旋转
  })
  // 禁止地图交互
  map.value.dragRotate.disable()
  map.value.doubleClickZoom.disable()
  map.value.touchZoomRotate.disable()
  map.value.scrollZoom.disable()
  map.value.dragPan.disable()

  map.value.on('load', () => {
    emit('mapLoad', map.value)
  })
}

function setFillColor(codes: string[]) {
  // 如果codes数组为空，则将所有城市设置为透明
  if (!codes || codes.length === 0) {
    map.value.setPaintProperty('city-layer', 'fill-color', 'transparent')
    return
  }

  // 设置表达式，根据ccode是否在codes数组中来决定颜色
  const fillColorExpression = [
    'case',
    ['in', ['get', 'ccode'], ['literal', codes]],
    '#7effe8', // 如果ccode在codes数组中，设置为蓝色
    'transparent' // 否则设置为透明
  ]

  // 应用颜色表达式到city-layer
  map.value.setPaintProperty('city-layer', 'fill-color', fillColorExpression)
}

defineExpose({
  setFillColor
})
onMounted(() => {
  initMap()
})
</script>

<style scoped lang="less">
.map {
  width: 100%;
  height: 100%;
}

:deep(.mapboxgl-canvas) {
  width: 380px !important;
  height: 310px !important;
}
</style>
