<template>
  <div class="page-container">
    <template v-if="pageLoad">
      <TopNav v-model="pageCurrent" />
      <BaseMap ref="mapRef" @mapLoad="mapLoad" @sceneLoad="sceneLoad" />
      <template v-if="childLoad">
        <Encyclopedia v-show="pageCurrent === 0" />
        <Distribution v-show="pageCurrent === 1" />
      </template>
    </template>
    <PageLoad v-if="showPageLoad" :progress="loadProgress" :text="loadText"></PageLoad>
  </div>
</template>

<script lang="ts" setup>
defineOptions({ name: 'Cyclopedia' })
import BaseMap from './components/BaseMap/index.vue'
import TopNav from './components/TopNav/index.vue'
import PageLoad from './components/PageLoad/index.vue'
import { loginApi, formDataApi } from '@gt/mis-components-web/apis'
import { useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import autofit from 'autofit.js'
import '@/assets/styles/onemap.less'
import { sceneKey, mapKey, mapRefKey, formDataKey } from './config'
import { initFormData } from './hooks/useMainHandler'
import { pageLayerSwitch } from './hooks/useMapLayers'
import { usePageStore } from '@/stores'

import Distribution from './children/Distribution/index.vue'
import Encyclopedia from './children/Encyclopedia/index.vue'

const pageStore = usePageStore()

const router = useRouter()
const pageLoad = ref(false)
const childLoad = ref(false)
const showPageLoad = ref(true) // 控制加载页面显示

const scene: any = ref(null)
const mapbox: any = ref(null)
const mapRef: any = ref()
// 提供给子组件使用
provide(sceneKey, scene)
provide(mapKey, mapbox)
provide(mapRefKey, mapRef)

// 加载状态管理
const loadProgress = ref(0)
const loadText = ref('初始化中...')

// 加载阶段枚举
const LOAD_STAGES = {
  INIT: { progress: 0, text: '初始化中...' },
  DATA_LOADING: { progress: 5, text: '数据加载中...' },
  MAP_LOADING: { progress: 50, text: '地图加载中...' },
  SCENE_LOADING: { progress: 60, text: '场景加载中...' },
  COMPONENTS_LOADING: { progress: 85, text: '页面元素加载中...' },
  COMPLETED: { progress: 100, text: '加载完成' }
}

/**
 * 更新加载进度
 * @param stage 加载阶段
 * @param customProgress 自定义进度值（可选）
 */
const updateLoadProgress = (stage: keyof typeof LOAD_STAGES, customProgress?: number) => {
  const stageInfo = LOAD_STAGES[stage]
  console.log('stageInfo', stageInfo)

  loadProgress.value = customProgress ?? stageInfo.progress
  loadText.value = stageInfo.text
}

/**
 * 场景加载完成回调
 */
const sceneLoad = async (map: any) => {
  scene.value = map
  childLoad.value = true

  updateLoadProgress('COMPONENTS_LOADING')

  // 模拟组件加载时间
  await new Promise((resolve) => setTimeout(resolve, 800))

  updateLoadProgress('COMPLETED')

  // 延迟隐藏加载页面
  setTimeout(() => {
    showPageLoad.value = false
  }, 500)
}

/**
 * 地图加载完成回调
 */
function mapLoad(map: any) {
  mapbox.value = map
  updateLoadProgress('SCENE_LOADING')
}
const pageCurrent = computed(() => pageStore.pageCurrent)

let formData: any = ref()
provide(formDataKey, formData)
/**
 * 初始化应用
 */
async function init() {
  try {
    updateLoadProgress('DATA_LOADING')

    // 加载表单数据
    const data = await initFormData({
      showError: true,
      onProgress: (completed, total) => {
        console.log(`数据加载进度: ${completed}/${total}`)
        // 数据加载阶段占总进度的25%（5% - 30%）
        const dataProgress =
          LOAD_STAGES.DATA_LOADING.progress +
          Math.floor(
            (completed / total) *
              (LOAD_STAGES.MAP_LOADING.progress - LOAD_STAGES.DATA_LOADING.progress)
          )
        updateLoadProgress('DATA_LOADING', dataProgress)
      }
    })

    // 数据加载完成，准备加载地图
    updateLoadProgress('MAP_LOADING')

    formData.value = data
    pageStore.setAreaDicts(data.admin_city, 'city')
    pageStore.setAreaDicts(data.admin_county, 'county')

    pageLoad.value = true

    // 初始化自适应布局
    await nextTick()
    autofit.init({
      dh: 1080,
      dw: 1920,
      el: '.page-container',
      resize: true,
      ignore: ['#map']
    })
  } catch (error) {
    console.error('初始化失败:', error)
    loadText.value = '加载失败，请刷新重试'
  }
}

// 自登录
onMounted(async () => {
  const token = localStorage.getItem('access_token')
  const userInfo = JSON.parse(localStorage.getItem('userInfo') as string)
  if (!token || !userInfo) {
    await autoLogin()
  } else if (userInfo && !userInfo.role_code.includes('data_view')) {
    localStorage.clear()
    await autoLogin
  }

  init()
})

onUnmounted(() => {
  autofit.off()
})

const autoLogin = async () => {
  try {
    const {
      access_token,
      refresh_token: newRefreshToken,
      user_id,
      token_type
    } = await loginApi.login('user01', 'user01')
    localStorage.setItem('access_token', `${token_type} ${access_token}`)
    localStorage.setItem('refresh_token', newRefreshToken)
    localStorage.setItem('user_id', user_id)
    const { list } = await formDataApi.searchFormData('tb_user_info_map', {
      filter: ['=', 'user_id', user_id]
    })
    localStorage.setItem('userInfo', JSON.stringify(list[0]))
  } catch (error) {
    message.error(error.msg)
  }
}

watch(
  () => pageStore.pageCurrent,
  () => {
    pageLayerSwitch(scene.value)
  }
)
</script>

<style lang="less" scoped>
.page-container {
  width: 100%;
  height: 100%;
  position: absolute;
  left: 0;
  top: 0;
  background-color: transparent;
}

.map-decoration {
  position: absolute;
  width: 100%;
  height: 100%;
  background-image: url('@/assets/images/onemap/map-grid.png');
  background-size: 70px 70px;
  background-repeat: repeat;
  opacity: 0.2;
  top: 0;
  left: 0;
  z-index: -2;
  pointer-events: none;
}
.map-decoration1 {
  position: absolute;
  width: 900px;
  height: 900px;
  background-image: url('@/assets/images/onemap/map-bg.png');
  background-size: 100% 100%;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  margin: auto;
  z-index: -1;
  pointer-events: none;
}
</style>
